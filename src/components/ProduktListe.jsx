import useStore from '../store/useStore';
import ProduktKort from './ProduktKort';
import './ProduktListe.css';

function ProduktListe() {
  const { getFiltrerteProduktter } = useStore();
  const produkter = getFiltrerteProduktter();

  return (
    <div className="produkt-liste">
      {produkter.map(produkt => (
        <ProduktKort key={produkt.id} produkt={produkt} />
      ))}
    </div>
  );
}

export default ProduktListe;
