# 🍔 Snackbaren

En React-applikasjon for bestilling av burgere, p<PERSON><PERSON><PERSON> og drikkevarer.

## 📋 Funksjonalitet

- **Produktvisning**: Viser alle tilgjengelige produkter med navn, bilde, pris og kategori
- **Filtrering**: Filtrer produkter etter kate<PERSON>i (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dr<PERSON><PERSON>er) eller vis alle
- **Handlekurv**: Legg til produkter i handlekurven og se totalpris
- **Antallstyring**: Øk eller reduser antall av hvert produkt i handlekurven
- **Responsivt design**: Fungerer på både desktop og mobil

## 🛠️ Teknologier

- **React** - UI-bibliotek
- **Vite** - Build tool og dev server
- **Zustand** - State management
- **CSS3** - Styling med moderne CSS-funksjoner

## 🚀 Kom i gang

1. Installer avhengigheter:
```bash
npm install
```

2. Start utviklingsserveren:
```bash
npm run dev
```

3. <PERSON><PERSON><PERSON> [http://localhost:5173](http://localhost:5173) i nettleseren

## 📁 Prosjektstruktur

```
src/
├── components/          # React-komponenter
│   ├── FilterKnapper.jsx
│   ├── ProduktListe.jsx
│   ├── ProduktKort.jsx
│   └── Handlekurv.jsx
├── store/              # Zustand state management
│   └── useStore.js
├── App.jsx             # Hovedkomponent
└── main.jsx           # Entry point
```

## 🎨 Funksjoner

### Produkter
- 4 forskjellige burgere
- 3 forskjellige pølser
- 4 forskjellige drikkevarer

### Handlekurv
- Vis produkter med antall og totalpris
- Øk/reduser antall med + og - knapper
- Automatisk oppdatering av totalpris

### Filtrering
- "Vis alle produkter" - viser alle produkter
- "Burgere" - viser kun burgere
- "Pølser" - viser kun pölser
- "Drikkevarer" - viser kun drikkevarer
