* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #333;
  background-color: #f8f9fa;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background: #f8f9fa;
}

#root {
  min-height: 100vh;
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  transition: all 0.3s ease;
}

button:focus {
  outline: 2px solid #4ecdc4;
  outline-offset: 2px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
}

img {
  max-width: 100%;
  height: auto;
}
