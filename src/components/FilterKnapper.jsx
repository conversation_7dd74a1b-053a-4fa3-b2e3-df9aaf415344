import useStore from '../store/useStore';
import './FilterKnapper.css';

function FilterKnapper() {
  const { aktivFilter, settFilter } = useStore();

  const filterKnapper = [
    { key: 'alle', tekst: 'Vis alle produkter' },
    { key: 'burger', tekst: '<PERSON><PERSON>' },
    { key: 'pølse', tekst: '<PERSON><PERSON><PERSON><PERSON>' },
    { key: 'drikkevare', tekst: '<PERSON><PERSON><PERSON><PERSON><PERSON>' }
  ];

  return (
    <div className="filter-knapper">
      {filterKnapper.map(filter => (
        <button
          key={filter.key}
          className={`filter-knapp ${aktivFilter === filter.key ? 'aktiv' : ''}`}
          onClick={() => settFilter(filter.key)}
        >
          {filter.tekst}
        </button>
      ))}
    </div>
  );
}

export default FilterKnapper;
