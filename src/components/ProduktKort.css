.produkt-kort {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.produkt-kort:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.produkt-kort.animasjon {
  animation: kjopAnimasjon 0.3s ease;
}

@keyframes kjopAnimasjon {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.produkt-bilde-container {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: #f8f9fa;
}

.produkt-bilde {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
  padding: 10px;
}

.produkt-kort:hover .produkt-bilde {
  transform: scale(1.1);
}

.kategori-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.produkt-info {
  padding: 1.5rem;
}

.produkt-navn {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
}

.produkt-pris {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: bold;
  color: #4ecdc4;
}

.kjop-knapp {
  width: 100%;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kjop-knapp:hover {
  background: linear-gradient(45deg, #44a08d, #4ecdc4);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.kjop-knapp:active {
  transform: translateY(0);
}
