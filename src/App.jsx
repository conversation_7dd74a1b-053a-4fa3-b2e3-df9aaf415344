import { useState, useEffect } from 'react';
import useStore from './store/useStore';
import ProduktListe from './components/ProduktListe';
import Handlekurv from './components/Handlekurv';
import FilterKnapper from './components/FilterKnapper';
import './App.css';

function App() {
  const { getAntallVarer } = useStore();
  const [visHandlekurv, setVisHandlekurv] = useState(false);

  return (
    <div className="app">
      <header className="header">
        <h1>🍔 Snackbaren</h1>
        <button
          className="handlekurv-knapp"
          onClick={() => setVisHandlekurv(!visHandlekurv)}
        >
          🛒 Handlekurv ({getAntallVarer()})
        </button>
      </header>

      <main className="main">
        <div className="innhold">
          <FilterKnapper />
          <ProduktListe />
        </div>

        {visHandlekurv && (
          <div className="handlekurv-overlay">
            <div className="handlekurv-modal">
              <button
                className="lukk-knapp"
                onClick={() => setVisHandlekurv(false)}
              >
                ✕
              </button>
              <Handlekurv />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
