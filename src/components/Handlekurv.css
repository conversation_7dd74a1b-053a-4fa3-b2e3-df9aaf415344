.handlekurv h2 {
  margin: 0 0 1.5rem 0;
  color: #333;
  text-align: center;
}

.tom-handlekurv {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}

.handlekurv-varer {
  margin-bottom: 1.5rem;
}

.handlekurv-vare {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  gap: 1rem;
}

.handlekurv-vare:last-child {
  border-bottom: none;
}

.vare-info {
  flex: 1;
}

.vare-info h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 1rem;
}

.vare-pris {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.antall-kontroller {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.antall-knapp {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease;
}

.antall-knapp:hover {
  background: #f0f0f0;
  border-color: #4ecdc4;
}

.antall-knapp.minus {
  color: #ff6b6b;
}

.antall-knapp.pluss {
  color: #4ecdc4;
}

.antall {
  min-width: 30px;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.vare-total {
  font-weight: bold;
  color: #4ecdc4;
  min-width: 80px;
  text-align: right;
}

.handlekurv-total {
  border-top: 2px solid #eee;
  padding-top: 1rem;
  text-align: center;
}

.handlekurv-total h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.bestill-knapp {
  width: 100%;
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.bestill-knapp:hover {
  background: linear-gradient(45deg, #ff8e53, #ff6b6b);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

@media (max-width: 480px) {
  .handlekurv-vare {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .antall-kontroller {
    align-self: center;
  }
  
  .vare-total {
    align-self: center;
    text-align: center;
  }
}
