.filter-knapper {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-knapp {
  background: white;
  border: 2px solid #ddd;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
}

.filter-knapp:hover {
  border-color: #4ecdc4;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.filter-knapp.aktiv {
  background: #4ecdc4;
  border-color: #4ecdc4;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

@media (max-width: 768px) {
  .filter-knapper {
    flex-direction: column;
    align-items: center;
  }
  
  .filter-knapp {
    width: 200px;
  }
}
