import useStore from '../store/useStore';
import './Handlekurv.css';

function Handlekurv() {
  const { handlekurv, getTotalPris, fjernFraHandlekurv, leggTilIHandlekurv } = useStore();

  if (handlekurv.length === 0) {
    return (
      <div className="handlekurv">
        <h2>🛒 Din handlekurv</h2>
        <p className="tom-handlekurv">Handlekurven er tom</p>
      </div>
    );
  }

  return (
    <div className="handlekurv">
      <h2>🛒 Din handlekurv</h2>
      
      <div className="handlekurv-varer">
        {handlekurv.map(vare => (
          <div key={vare.id} className="handlekurv-vare">
            <div className="vare-info">
              <h4>{vare.navn}</h4>
              <p className="vare-pris">{vare.pris} kr</p>
            </div>
            
            <div className="antall-kontroller">
              <button 
                className="antall-knapp minus"
                onClick={() => fjernFraHandlekurv(vare.id)}
              >
                -
              </button>
              <span className="antall">{vare.antall}</span>
              <button 
                className="antall-knapp pluss"
                onClick={() => leggTilIHandlekurv(vare)}
              >
                +
              </button>
            </div>
            
            <div className="vare-total">
              {vare.pris * vare.antall} kr
            </div>
          </div>
        ))}
      </div>
      
      <div className="handlekurv-total">
        <h3>Total: {getTotalPris()} kr</h3>
      </div>
      
      <button className="bestill-knapp">
        🎉 Bestill nå
      </button>
    </div>
  );
}

export default Handlekurv;
