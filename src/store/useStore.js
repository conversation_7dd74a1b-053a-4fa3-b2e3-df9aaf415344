import { create } from 'zustand';

// Produktdata
const produkter = [
  // Burgere
  {
    id: 1,
    navn: 'Klassisk Burger',
    pris: 89,
    kategori: 'burger',
    bilde: '/burger.jpeg'
  },
  {
    id: 2,
    navn: 'Cheeseburger',
    pris: 99,
    kategori: 'burger',
    bilde: '/burger.jpeg'
  },
  {
    id: 3,
    navn: 'Bacon Burger',
    pris: 119,
    kategori: 'burger',
    bilde: '/burger.jpeg'
  },
  {
    id: 4,
    navn: 'Kjempegrill',
    pris: 139,
    kategori: 'burger',
    bilde: '/burger.jpeg'
  },
  
  // <PERSON><PERSON><PERSON><PERSON>
  {
    id: 5,
    navn: 'Wienerpølse',
    pris: 35,
    kategori: 'pølse',
    bilde: '/polse.jpg'
  },
  {
    id: 6,
    navn: 'Grillpølse',
    pris: 45,
    kategori: 'pølse',
    bilde: '/polse.jpg'
  },
  {
    id: 7,
    navn: '<PERSON><PERSON>',
    pris: 55,
    kategori: 'pølse',
    bilde: '/polse.jpg'
  },
  
  // <PERSON><PERSON><PERSON><PERSON><PERSON>
  {
    id: 8,
    navn: 'Cola',
    pris: 25,
    kategori: 'drikkevare',
    bilde: '/cola1.avif'
  },
  {
    id: 9,
    navn: 'Sprite',
    pris: 25,
    kategori: 'drikkevare',
    bilde: '/sprite1.avif'
  },
  {
    id: 10,
    navn: 'Øl',
    pris: 45,
    kategori: 'drikkevare',
    bilde: '/ol1.jpg'
  },
  {
    id: 11,
    navn: 'Kaffe',
    pris: 30,
    kategori: 'drikkevare',
    bilde: '/kaffe1.jpeg'
  }
];

const useStore = create((set, get) => ({
  // State
  produkter: produkter,
  handlekurv: [],
  aktivFilter: 'alle',
  
  // Actions
  leggTilIHandlekurv: (produkt) => {
    const { handlekurv } = get();
    const eksisterendeProdukt = handlekurv.find(item => item.id === produkt.id);
    
    if (eksisterendeProdukt) {
      // Øk antallet hvis produktet allerede finnes
      set({
        handlekurv: handlekurv.map(item =>
          item.id === produkt.id
            ? { ...item, antall: item.antall + 1 }
            : item
        )
      });
    } else {
      // Legg til nytt produkt
      set({
        handlekurv: [...handlekurv, { ...produkt, antall: 1 }]
      });
    }
  },
  
  fjernFraHandlekurv: (produktId) => {
    const { handlekurv } = get();
    const produkt = handlekurv.find(item => item.id === produktId);
    
    if (produkt && produkt.antall > 1) {
      // Reduser antallet
      set({
        handlekurv: handlekurv.map(item =>
          item.id === produktId
            ? { ...item, antall: item.antall - 1 }
            : item
        )
      });
    } else {
      // Fjern produktet helt
      set({
        handlekurv: handlekurv.filter(item => item.id !== produktId)
      });
    }
  },
  
  settFilter: (filter) => {
    set({ aktivFilter: filter });
  },
  
  // Getters
  getFiltrerteProduktter: () => {
    const { produkter, aktivFilter } = get();
    if (aktivFilter === 'alle') {
      return produkter;
    }
    return produkter.filter(produkt => produkt.kategori === aktivFilter);
  },
  
  getTotalPris: () => {
    const { handlekurv } = get();
    return handlekurv.reduce((total, item) => total + (item.pris * item.antall), 0);
  },
  
  getAntallVarer: () => {
    const { handlekurv } = get();
    return handlekurv.reduce((total, item) => total + item.antall, 0);
  }
}));

export default useStore;
