import { useState } from 'react';
import useStore from '../store/useStore';
import './ProduktKort.css';

function ProduktKort({ produkt }) {
  const { leggTilIHandlekurv } = useStore();
  const [animasjon, setAnimasjon] = useState(false);

  const handleKjop = () => {
    leggTilIHandlekurv(produkt);
    
    // Liten animasjon når man legger til i handlekurv
    setAnimasjon(true);
    setTimeout(() => setAnimasjon(false), 300);
  };

  const handleBildeFeil = (e) => {
    // Fallback til placeholder hvis bildet ikke finnes
    e.target.src = `https://via.placeholder.com/200x150/cccccc/666666?text=${encodeURIComponent(produkt.navn)}`;
  };

  return (
    <div className={`produkt-kort ${animasjon ? 'animasjon' : ''}`}>
      <div className="produkt-bilde-container">
        <img 
          src={produkt.bilde} 
          alt={produkt.navn}
          className="produkt-bilde"
          onError={handleBildeFeil}
        />
        <div className="kategori-badge">
          {produkt.kategori}
        </div>
      </div>
      
      <div className="produkt-info">
        <h3 className="produkt-navn">{produkt.navn}</h3>
        <p className="produkt-pris">{produkt.pris} kr</p>
        
        <button 
          className="kjop-knapp"
          onClick={handleKjop}
        >
          🛒 Kjøp
        </button>
      </div>
    </div>
  );
}

export default ProduktKort;
